// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sen<PERSON> from "@sentry/nextjs";

fetch("/actuator/info").then(res => {
   res.json().then(body => {
    const environment = body['environment']
    Sentry.init({
      dsn: "https://<EMAIL>/4505997156679680",
      environment: environment,

      tracesSampleRate:  0,

      replaysSessionSampleRate: 0,
      replaysOnErrorSampleRate: 0,

      debug: false,
    });

  })
})
