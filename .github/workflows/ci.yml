name: CI

env:
  REGISTRY: me-jeddah-1.ocir.io
  IMAGE: axv3zmfeydzj/frontend

on:
  push:
    branches:
      - main
      - release
      - 'feature/eba-**'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Login to OCIR
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.OCIR_USERNAME }}
          password: ${{ secrets.OCIR_PASSWORD }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker images
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE }}:${{ github.sha }}
          build-args: |
            "SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}"

  dev-deploy:
    concurrency:
      group: deploy
      cancel-in-progress: false
    needs: [ build ]
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          repository: Ebana-sa/infra
          ref: main
          ssh-key: "${{ secrets.INFRA_SSH_KEY }}"

      - uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: "${{ secrets.INFRA_SSH_KEY }}"

      - name: Update image tag
        uses: mikefarah/yq@master
        with:
          cmd: yq -i '(.images[] | select( .name == "frontend") | .newTag) = "${{ github.sha }}"' dev/kustomization.yaml

      - uses: EndBug/add-and-commit@v9
        with:
          default_author: github_actions
          message: 'dev/frontend: release ${{ github.sha }}'
          fetch: false
          push: false

      - run: git push

  test-deploy:
    concurrency:
      group: deploy
      cancel-in-progress: false
    needs: [ build ]
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          repository: Ebana-sa/infra
          ref: main
          ssh-key: "${{ secrets.INFRA_SSH_KEY }}"

      - uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: "${{ secrets.INFRA_SSH_KEY }}"

      - name: Update image tag
        uses: mikefarah/yq@master
        with:
          cmd: yq -i '(.images[] | select( .name == "frontend") | .newTag) = "${{ github.sha }}"' test/kustomization.yaml

      - uses: EndBug/add-and-commit@v9
        with:
          default_author: github_actions
          message: 'test/frontend: release ${{ github.sha }}'
          fetch: false
          push: false

      - run: git push



  staging-deploy:
    concurrency:
      group: deploy
      cancel-in-progress: false
    needs: [ build ]
    if: github.ref == 'refs/heads/release'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          repository: Ebana-sa/infra
          ref: main
          ssh-key: "${{ secrets.INFRA_SSH_KEY }}"

      - uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: "${{ secrets.INFRA_SSH_KEY }}"

      - name: Update image tag
        uses: mikefarah/yq@master
        with:
          cmd: yq -i '(.images[] | select( .name == "frontend") | .newTag) = "${{ github.sha }}"' staging/kustomization.yaml

      - uses: EndBug/add-and-commit@v9
        with:
          default_author: github_actions
          message: 'staging/frontend: release ${{ github.sha }}'
          fetch: false
          push: false

      - run: git push
