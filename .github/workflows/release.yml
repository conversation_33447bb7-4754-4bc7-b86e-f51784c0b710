name: Release

on:
  workflow_dispatch:

permissions:
  contents: write

jobs:
  release:
    if: github.ref == 'refs/heads/main'
    runs-on: [ ubuntu-latest ]
    steps:
      - name: Checkout code ${{ github.ref_name }}
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: release

      - name: Save branch
        run: |
          B=release-$(date -u +"%Y%m%d%H%M")
          git checkout -b $B && git push --set-upstream origin $B

      - name: Reset to main
        run: |
          git checkout release
          git reset --hard origin/main
          git push --force-with-lease origin release

  staging-deploy:
    needs: [release]
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          repository: Ebana-sa/infra
          ref: main
          ssh-key: "${{ secrets.INFRA_SSH_KEY }}"

      - uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: "${{ secrets.INFRA_SSH_KEY }}"

      - name: Update image tag
        uses: mikefarah/yq@master
        with:
          cmd: yq -i '(.images[] | select( .name == "frontend") | .newTag) = "${{ github.sha }}"' staging/kustomization.yaml

      - uses: EndBug/add-and-commit@v9
        with:
          default_author: github_actions
          message: 'staging/frontend: release ${{ github.sha }}'
          fetch: false
          push: false

      - run: git push