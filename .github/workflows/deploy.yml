name: Deploy

on:
  workflow_dispatch:
    inputs:
      name:
        required: true
        description: The image name
        default: frontend
        type: string
      newName:
        required: true
        description: The new image name
        default: me-jeddah-1.ocir.io/axv3zmfeydzj/frontend
        type: string
      tag:
        required: true
        description: The release tag/commit
        type: string
      environment:
        required: true
        description: The target environment
        type: choice
        options:
          - dev
          - staging
          - production

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the deployment repository
        uses: actions/checkout@v3
        with:
          repository: Ebana-sa/infra
          ref: main
          ssh-key: "${{ secrets.INFRA_SSH_KEY }}"

      - uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: "${{ secrets.INFRA_SSH_KEY }}"

      - name: Update image name
        uses: mikefarah/yq@master
        with:
          cmd: yq -i '(.images[] | select( .name == "${{ inputs.name }}") | .newName) = "${{ inputs.newName }}"' ${{ inputs.environment }}/kustomization.yaml

      - name: Update image tag
        uses: mikefarah/yq@master
        with:
          cmd: yq -i '(.images[] | select( .name == "${{ inputs.name }}") | .newTag) = "${{ inputs.tag }}"' ${{ inputs.environment }}/kustomization.yaml

      - uses: EndBug/add-and-commit@v9
        with:
          default_author: github_actions
          message: '${{ inputs.environment }}/${{ inputs.name }}: release ${{ github.sha }}'
          fetch: false
          push: false

      - run: git push
