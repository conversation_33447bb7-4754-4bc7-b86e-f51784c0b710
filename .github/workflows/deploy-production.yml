name: Deploy Production 🚀

on:
  workflow_dispatch:

jobs:
  deploy:
    if: github.ref == 'refs/heads/release'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the deployment repository
        uses: actions/checkout@v3
        with:
          repository: Ebana-sa/infra
          ref: main
          ssh-key: "${{ secrets.INFRA_SSH_KEY }}"

      - uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: "${{ secrets.INFRA_SSH_KEY }}"

      - name: Update image name
        uses: mikefarah/yq@master
        with:
          cmd: yq -i '(.images[] | select( .name == "frontend") | .newName) = "me-jeddah-1.ocir.io/axv3zmfeydzj/frontend"' production/kustomization.yaml

      - name: Update image tag
        uses: mikefarah/yq@master
        with:
          cmd: yq -i '(.images[] | select( .name == "frontend") | .newTag) = "${{ github.sha }}"' production/kustomization.yaml

      - uses: EndBug/add-and-commit@v9
        with:
          default_author: github_actions
          message: 'production/frontend: release ${{ github.sha }}'
          fetch: false
          push: false

      - run: git push
