'use client'

import { FilterIcon2, RiyalNewSignIcon, SearchIcon } from '@/components/EbanaIcons'
import DebounceInput from '@/components/input/DebounceInput'
import { graphql } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import {
  Box,
  Button,
  Field,
  Flex,
  Image,
  InputGroup,
  Stack,
  Table,
} from '@chakra-ui/react'
import React from 'react'
import { useQuery } from 'urql'
import { BiChevronDown } from 'react-icons/bi'
import { Tooltip } from '@/components/ui/tooltip'

EquityList.fragment = graphql(`
    fragment EquityListFragment on Equity {
        internalMarketEnabled
        internalMarketSellOrders {
            formatted
        }
        positions {
            company {
                tradeName
                logo {
                    url
                }
            }
            shareClass {
                name
            }
            marketValue {
                formatted
            }
            profitAndLossPercent {
                formatted
                value
            }
            marketSellCreateForm {
                available
            }
        }
    }
`)

const EquityListQuery = graphql(`
    query EquityListQuery {
        me {
            equities {
                ...EquityListFragment
            }
        }
    }
`, [EquityList.fragment])

export function EquityList() {
  const { t } = useEbanaLocale()
  const [search, setSearch] = React.useState('')
  const [sortBy, setSortBy] = React.useState('company')

  const [{ data, error, fetching }] = useQuery({
    query: EquityListQuery,
  })

  const equities = data?.me?.equities

  // Filter equities based on search term
  const filteredEquities = React.useMemo(() => {
    if (!equities) return []
    
    if (!search.trim()) {
      return equities
    }
    
    const searchLower = search.toLowerCase().trim()
    
    return equities.filter(equity => 
      equity.positions.some(position => 
        position.company?.tradeName?.toLowerCase().includes(searchLower) ||
        position.shareClass?.name?.toLowerCase().includes(searchLower)
      )
    ).map(equity => ({
      ...equity,
      positions: equity.positions.filter(position => 
        position.company?.tradeName?.toLowerCase().includes(searchLower) ||
        position.shareClass?.name?.toLowerCase().includes(searchLower)
      )
    }))
  }, [equities, search])

  if (error) {
    return (
      <Box p={4} color="red.500">
        Error loading equity data
      </Box>
    )
  }

  if (fetching && !data) {
    return (
      <Box p={4}>
        Loading...
      </Box>
    )
  }

  return (
    <Stack gap={0} borderRadius="10px" bg="#1A2234" overflow="hidden">
      {/* Header */}
      <Flex
        p={{ base: '1rem', md: '1.5rem' }}
        justifyContent="space-between"
        alignItems="center"
      >
        <Box color="white" textStyle="h4" fontWeight={600}>
          {t('companies')}
        </Box>

        <Flex gap="1rem" alignItems="center">
          <Field.Root>
            <InputGroup
              endElement={<SearchIcon stroke="#B0B7C3" width="18px" height="18px" />}
              width="300px"
              borderRadius="10px"
            >
              <DebounceInput
                value={search}
                onChange={setSearch}
                placeholder={t('equity_list_search_placeholder')}
                borderRadius="10px"
                bg="transparent"
                borderColor="rgba(255, 255, 255, 0.20)"
                color="white"
                _placeholder={{ color: '#B0B7C3' }}
                py="0.75rem"
                px="1rem"
              />
            </InputGroup>
          </Field.Root>

          <Button
            bg="transparent"
            color="white"
            borderRadius="10px"
            border="1px solid rgba(255, 255, 255, 0.20)"
            px="1rem"
            py="0.65rem"
            fontWeight={500}
            _hover={{ bg: "rgba(255, 255, 255, 0.15)" }}
            // flex
            flex={{ base: '1', md: 'none' }}
            alignItems="center"
          >

            <FilterIcon2 stroke="#B0B7C3" />

            <Box color='#B0B7C3'>
              {t('equity_list_filter_placeholder')}
            </Box>
          </Button>

          {/* Sort */}
          <Button
            bg="transparent"
            color="white"
            borderRadius="10px"
            border="1px solid rgba(255, 255, 255, 0.20)"
            px="1rem"
            py="0.65rem"
            height="40px"
            fontWeight={500}
            _hover={{ bg: "rgba(255, 255, 255, 0.15)" }}
            // flex
            flex={{ base: '1', md: 'none' }}
            alignItems="center"
          >
            <Box color="#B0B7C3">
              {t('equity_list_sort_by_placeholder')}
            </Box>

            <BiChevronDown color="#B0B7C3" />
          </Button>
        </Flex>
      </Flex>

      {/* Table */}
      <Box
        maxW="100%"
        overflowX="auto"
        css={{
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          '&::MsOverflowStyle': 'none',
          scrollbarWidth: 'none',
        }}
      >
        <Table.ScrollArea
          px={{ base: '1rem', md: '1.5rem' }}
          minW={{ base: '800px', md: 'unset' }}>
          <Table.Root style={{ borderCollapse: 'separate', borderSpacing: '0 0.5em' }}>
            <Table.Header display={{ base: 'none', md: 'table-header-group' }} bg="transparent">
              <Table.Row>
                <Table.ColumnHeader
                  borderY="1px solid rgba(255, 255, 255, 0.10)"
                  fontWeight={400}
                  color="#B0B7C3"
                  bg="transparent"
                  py="0.3rem"
                >
                  <Box>{t('Company')}</Box>
                </Table.ColumnHeader>

                <Table.ColumnHeader
                  borderY="1px solid rgba(255, 255, 255, 0.10)"
                  fontWeight={400}
                  color="#B0B7C3"
                  bg="transparent"
                  textAlign="center"
                  py="0.3rem"
                >
                  <Box>{t('sell_orders')}</Box>
                </Table.ColumnHeader>

                <Table.ColumnHeader
                  borderY="1px solid rgba(255, 255, 255, 0.10)"
                  fontWeight={400}
                  color="#B0B7C3"
                  bg="transparent"
                  textAlign="center"
                  py="0.3rem"
                >
                  <Box>{t('value')}</Box>
                </Table.ColumnHeader>

                <Table.ColumnHeader
                  borderY="1px solid rgba(255, 255, 255, 0.10)"
                  fontWeight={400}
                  color="#B0B7C3"
                  bg="transparent"
                  textAlign="center"
                  py="0.3rem"
                >
                  <Box>{t('change')}</Box>
                </Table.ColumnHeader>

                <Table.ColumnHeader
                  borderY="1px solid rgba(255, 255, 255, 0.10)"
                  fontWeight={400}
                  color="#B0B7C3"
                  bg="transparent"
                  textAlign="center"
                  py="0.3rem"
                >
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {filteredEquities.flatMap((equity) =>
                equity.positions.map((position, index) => {
                  const sellOrders = equity.internalMarketSellOrders?.formatted
                  const hasValue = position.marketValue?.formatted
                  const isAvailable = equity.internalMarketEnabled

                  return (
                    <Table.Row
                      key={`${index}-${position.company?.tradeName}-${position.shareClass?.name}`}
                      cursor="pointer"
                      _hover={{ bg: "rgba(255, 255, 255, 0.05)" }}
                    >
                      {/* Company */}
                      <Table.Cell
                        color="white"
                        bg="transparent"
                        textStyle="body4"
                        fontWeight={500}
                        px="1.5rem"
                        py="1rem"
                      >
                        <Flex gap="12px" alignItems="center">
                          <Box
                            height="40px"
                            width="40px"
                            minW="40px"
                            borderRadius="50%"
                            overflow="hidden"
                            bg="white"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                          >
                            <Image
                              maxH="32px"
                              maxW="32px"
                              src={position.company?.logo?.url}
                              alt={position.company?.tradeName || ''}
                            />
                          </Box>
                          <Stack gap={0}>
                            <Box fontWeight={600} fontSize="1rem">
                              {position.company?.tradeName}
                            </Box>
                            <Box
                              fontSize="1rem"
                              color="#B0B7C3"
                              display={{ base: 'none', md: 'block' }}
                            >
                              {position.shareClass?.name}
                            </Box>
                          </Stack>
                        </Flex>
                      </Table.Cell>

                      <Table.Cell
                        color="white"
                        bg="transparent"
                        textAlign="center"
                        px="1rem"
                        py="1rem"
                      >
                        <Box fontWeight={500}>
                          {sellOrders}
                        </Box>
                      </Table.Cell>

                      <Table.Cell
                        color="white"
                        bg="transparent"
                        textAlign="center"
                        px="1rem"
                        py="1rem"
                      >
                        {hasValue ? (
                          <Box dir="ltr" fontWeight={500}>
                            <RiyalNewSignIcon fill="white" height="13px" mr="4px" />
                            {position.marketValue?.formatted?.replace('SAR', '').replace('﷼', '')}
                          </Box>
                        ) : (
                          <Box color="#B0B7C3">-</Box>
                        )}
                      </Table.Cell>

                      <Table.Cell
                        bg="transparent"
                        textAlign="center"
                        px="1rem"
                        py="1rem"
                      >
                        {
                          position.profitAndLossPercent?.value ? (
                            position.profitAndLossPercent.value > 0 ? (
                              <Box color="#00BFB2" fontWeight={500}>
                                +{position.profitAndLossPercent.formatted}
                              </Box>
                            ) : (
                              <Box color="primary.400" fontWeight={500}>
                                {position.profitAndLossPercent.formatted}
                              </Box>
                            )
                          ) : (
                            <Box>0%</Box>
                          )
                        }
                      </Table.Cell>

                      {/* Action */}
                      <Table.Cell
                        bg="transparent"
                        textAlign="center"
                        px="1rem"
                        py="1rem"
                      >
                        <Tooltip
                          content={
                            <Box color="black" fontWeight={600} width="400px" fontSize="1rem">This company has not allowed internal exchange</Box>
                          }
                          positioning={{ placement: 'top-start' }}
                          triggerProps={{
                            style: {
                              width: 'fit-content',
                            }
                          }}
                          contentProps={{
                            style: {
                              backgroundColor: 'white',
                              width: '400px',
                              maxWidth: 'fit-content',
                              padding: '8px 12px',
                              borderRadius: '8px',
                            }
                          }}
                        >
                          <Button
                            bg={isAvailable ? "primary.200" : "#6B7280"}
                            color="white"
                            borderRadius="10px"
                            width="112px"
                            py="0.5rem"
                            height="34px"
                            fontWeight={600}
                            _disabled={{
                              bg: "#6B7280",
                              cursor: "not-allowed"
                            }}
                            disabled={!isAvailable}
                          >
                            {t('view')}
                          </Button>
                        </Tooltip>
                      </Table.Cell>
                    </Table.Row>
                  )
                })
              )}
            </Table.Body>
          </Table.Root>
        </Table.ScrollArea>
      </Box>

      {/* See All Button */}
      {filteredEquities?.length > 0 && (
        <Flex
          justifyContent="center"
          p="1rem"
          borderTop="1px solid rgba(255, 255, 255, 0.10)"
        >
          <Button
            variant="plain"
            color="primary.200"
            fontWeight={500}
            _hover={{ textDecoration: "underline" }}
          >
            {t('see_all')}
          </Button>
        </Flex>
      )}

      {/* Empty State */}
      {((search && filteredEquities?.length === 0) || (equities?.length === 0)) && !fetching && (
        <Box
          textAlign="center"
          py="3rem"
          color="#B0B7C3"
        >
          <Box fontWeight={500}>
            {search ? t('empty_content') : t('no_data_available')}
          </Box>
          {search && filteredEquities?.length === 0 && equities?.length > 0 && (
            <Box mt="0.5rem">
            </Box>
          )}
        </Box>
      )}
    </Stack>
  )
}
