'use client'

import { RiyalNewSignIcon, SearchIcon } from '@/components/EbanaIcons'
import DebounceInput from '@/components/input/DebounceInput'
import { graphql } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Field, Flex, Image, InputGroup, Stack, Table } from '@chakra-ui/react'
import React from 'react'
import { useQuery } from 'urql'
import { BiChevronDown } from 'react-icons/bi'
import { Tooltip } from '@/components/ui/tooltip'
import { DialogBody, DialogCloseTrigger, DialogContent, DialogHeader, DialogRoot } from '@/components/ui/dialog'

// Sort Option Component
interface SortOptionProps {
  value: string
  label: string
  isSelected: boolean
  onClick: (value: string) => void
}

const SortOption: React.FC<SortOptionProps> = ({ value, label, isSelected, onClick }) => {
  return (
    <Box
      cursor='pointer'
      _hover={{ bg: '#F9FAFB' }}
      py='8px'
      px='12px'
      borderRadius='8px'
      onClick={() => onClick(value)}>
      <Flex justifyContent='space-between' alignItems='center'>
        <Box fontSize='16px' fontWeight={400} color='#101828'>
          {label}
        </Box>
        {isSelected && <Box color='#00BFB2'>✓</Box>}
      </Flex>
    </Box>
  )
}

EquityList.fragment = graphql(`
  fragment EquityListFragment on Equity {
    internalMarketEnabled
    internalMarketSellOrders {
      formatted
      value
    }
    positions {
      company {
        tradeName
        logo {
          url
        }
      }
      shareClass {
        name
      }
      marketValue {
        formatted
        value
      }
      profitAndLossPercent {
        formatted
        value
      }
      marketSellCreateForm {
        available
      }
    }
  }
`)

const EquityListQuery = graphql(
  `
    query EquityListQuery {
      me {
        equities {
          ...EquityListFragment
        }
      }
    }
  `,
  [EquityList.fragment]
)

export function EquityList() {
  const { t } = useEbanaLocale()
  
  // Sort options configuration
  const sortOptions = [
    {
      value: 'sellOrdersHighToLow',
      label: t('sort_sell_orders_high_to_low')
    },
    {
      value: 'sellOrdersLowToHigh',
      label: t('sort_sell_orders_low_to_high')
    },
    {
      value: 'marketValueHighToLow',
      label: t('sort_market_value_high_to_low')
    },
    {
      value: 'marketValueLowToHigh',
      label: t('sort_market_value_low_to_high')
    },
    {
      value: 'profitLossHighToLow',
      label: t('sort_profit_loss_high_to_low')
    },
    {
      value: 'profitLossLowToHigh',
      label: t('sort_profit_loss_low_to_high')
    }
  ]
  const [search, setSearch] = React.useState('')
  const [sortBy, setSortBy] = React.useState('')
  const [sortDialogOpen, setSortDialogOpen] = React.useState(false)
  const [tempSortBy, setTempSortBy] = React.useState(sortBy)

  const [{ data, error, fetching }] = useQuery({
    query: EquityListQuery,
  })

  const equities = data?.me?.equities

  // Filter and sort equities based on search term and sort criteria
  const filteredEquities = React.useMemo(() => {
    if (!equities) return []

    let result = equities

    // Filter by search
    if (search.trim()) {
      const searchLower = search.toLowerCase().trim()
      result = equities
        .filter((equity) =>
          equity.positions.some(
            (position) =>
              position.company?.tradeName?.toLowerCase().includes(searchLower) ||
              position.shareClass?.name?.toLowerCase().includes(searchLower)
          )
        )
        .map((equity) => ({
          ...equity,
          positions: equity.positions.filter(
            (position) =>
              position.company?.tradeName?.toLowerCase().includes(searchLower) ||
              position.shareClass?.name?.toLowerCase().includes(searchLower)
          ),
        }))
    }

    // Sort the results
    return [...result].sort((a, b) => {
      switch (sortBy) {
        case 'sellOrdersHighToLow':
          return (b.internalMarketSellOrders?.value || 0) - (a.internalMarketSellOrders?.value || 0)
        case 'sellOrdersLowToHigh':
          return (a.internalMarketSellOrders?.value || 0) - (b.internalMarketSellOrders?.value || 0)
        case 'marketValueHighToLow':
          // For market value, we need to get the total across all positions
          const aTotal = a.positions.reduce((sum, pos) => sum + (pos.marketValue?.value || 0), 0)
          const bTotal = b.positions.reduce((sum, pos) => sum + (pos.marketValue?.value || 0), 0)
          return bTotal - aTotal
        case 'marketValueLowToHigh':
          const aTotal2 = a.positions.reduce((sum, pos) => sum + (pos.marketValue?.value || 0), 0)
          const bTotal2 = b.positions.reduce((sum, pos) => sum + (pos.marketValue?.value || 0), 0)
          return aTotal2 - bTotal2
        case 'profitLossHighToLow':
          // For profit/loss, we need to get the average across all positions
          const aAvg =
            a.positions.reduce((sum, pos) => sum + (pos.profitAndLossPercent?.value || 0), 0) / a.positions.length
          const bAvg =
            b.positions.reduce((sum, pos) => sum + (pos.profitAndLossPercent?.value || 0), 0) / b.positions.length
          return bAvg - aAvg
        case 'profitLossLowToHigh':
          const aAvg2 =
            a.positions.reduce((sum, pos) => sum + (pos.profitAndLossPercent?.value || 0), 0) / a.positions.length
          const bAvg2 =
            b.positions.reduce((sum, pos) => sum + (pos.profitAndLossPercent?.value || 0), 0) / b.positions.length
          return aAvg2 - bAvg2
        default:
          // dont sort
          return 0
      }
    })
  }, [equities, search, sortBy])

  if (error) {
    return (
      <Box p={4} color='red.500'>
        Error loading equity data
      </Box>
    )
  }

  if (fetching && !data) {
    return <Box p={4}>Loading...</Box>
  }

  return (
    <Stack gap={0} borderRadius='10px' bg='#1A2234' overflow='hidden'>
      {/* Header */}
      <Flex p={{ base: '1rem', md: '1.5rem' }} justifyContent='space-between' alignItems='center'>
        <Box color='white' textStyle='h4' fontWeight={600}>
          {t('companies')}
        </Box>

        <Flex gap='1rem' alignItems='center'>
          <Field.Root>
            <InputGroup
              endElement={<SearchIcon stroke='#B0B7C3' width='18px' height='18px' />}
              width='300px'
              borderRadius='10px'>
              <DebounceInput
                value={search}
                onChange={setSearch}
                placeholder={t('equity_list_search_placeholder')}
                borderRadius='10px'
                bg='transparent'
                borderColor='rgba(255, 255, 255, 0.20)'
                color='white'
                _placeholder={{ color: '#B0B7C3' }}
                py='0.75rem'
                px='1rem'
              />
            </InputGroup>
          </Field.Root>

          {/* Sort */}
          <Button
            bg='transparent'
            color='white'
            borderRadius='10px'
            border='1px solid rgba(255, 255, 255, 0.20)'
            px='1rem'
            py='0.65rem'
            height='40px'
            fontWeight={500}
            _hover={{ bg: 'rgba(255, 255, 255, 0.15)' }}
            flex={{ base: '1', md: 'none' }}
            alignItems='center'
            onClick={() => {
              setTempSortBy(sortBy)
              setSortDialogOpen(true)
            }}>
            <Box color='#B0B7C3'>{t('equity_list_sort_by_placeholder')}</Box>

            <BiChevronDown color='#B0B7C3' />
          </Button>
        </Flex>
      </Flex>

      {/* Table */}
      <Box
        maxW='100%'
        overflowX='auto'
        css={{
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          '&::MsOverflowStyle': 'none',
          scrollbarWidth: 'none',
        }}>
        <Table.ScrollArea px={{ base: '1rem', md: '1.5rem' }} minW={{ base: '800px', md: 'unset' }}>
          <Table.Root style={{ borderCollapse: 'separate', borderSpacing: '0 0.5em' }}>
            <Table.Header display={{ base: 'none', md: 'table-header-group' }} bg='transparent'>
              <Table.Row>
                <Table.ColumnHeader
                  borderY='1px solid rgba(255, 255, 255, 0.10)'
                  fontWeight={400}
                  color='#B0B7C3'
                  bg='transparent'
                  py='0.3rem'>
                  <Box>{t('Company')}</Box>
                </Table.ColumnHeader>

                <Table.ColumnHeader
                  borderY='1px solid rgba(255, 255, 255, 0.10)'
                  fontWeight={400}
                  color='#B0B7C3'
                  bg='transparent'
                  textAlign='center'
                  py='0.3rem'>
                  <Box>{t('sell_orders')}</Box>
                </Table.ColumnHeader>

                <Table.ColumnHeader
                  borderY='1px solid rgba(255, 255, 255, 0.10)'
                  fontWeight={400}
                  color='#B0B7C3'
                  bg='transparent'
                  textAlign='center'
                  py='0.3rem'>
                  <Box>{t('value')}</Box>
                </Table.ColumnHeader>

                <Table.ColumnHeader
                  borderY='1px solid rgba(255, 255, 255, 0.10)'
                  fontWeight={400}
                  color='#B0B7C3'
                  bg='transparent'
                  textAlign='center'
                  py='0.3rem'>
                  <Box>{t('change')}</Box>
                </Table.ColumnHeader>

                <Table.ColumnHeader
                  borderY='1px solid rgba(255, 255, 255, 0.10)'
                  fontWeight={400}
                  color='#B0B7C3'
                  bg='transparent'
                  textAlign='center'
                  py='0.3rem'></Table.ColumnHeader>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {filteredEquities.flatMap((equity) =>
                equity.positions.map((position, index) => {
                  const sellOrders = equity.internalMarketSellOrders?.formatted
                  const hasValue = position.marketValue?.formatted
                  const isAvailable = equity.internalMarketEnabled

                  return (
                    <Table.Row
                      key={`${index}-${position.company?.tradeName}-${position.shareClass?.name}`}
                      cursor='pointer'
                      _hover={{ bg: 'rgba(255, 255, 255, 0.05)' }}>
                      {/* Company */}
                      <Table.Cell
                        color='white'
                        bg='transparent'
                        textStyle='body4'
                        fontWeight={500}
                        px='1.5rem'
                        py='1rem'>
                        <Flex gap='12px' alignItems='center'>
                          <Box
                            height='40px'
                            width='40px'
                            minW='40px'
                            borderRadius='50%'
                            overflow='hidden'
                            bg='white'
                            display='flex'
                            alignItems='center'
                            justifyContent='center'>
                            <Image
                              maxH='32px'
                              maxW='32px'
                              src={position.company?.logo?.url}
                              alt={position.company?.tradeName || ''}
                            />
                          </Box>
                          <Stack gap={0}>
                            <Box fontWeight={600} fontSize='1rem'>
                              {position.company?.tradeName}
                            </Box>
                            <Box fontSize='1rem' color='#B0B7C3' display={{ base: 'none', md: 'block' }}>
                              {position.shareClass?.name}
                            </Box>
                          </Stack>
                        </Flex>
                      </Table.Cell>

                      <Table.Cell color='white' bg='transparent' textAlign='center' px='1rem' py='1rem'>
                        <Box fontWeight={500}>{sellOrders}</Box>
                      </Table.Cell>

                      <Table.Cell color='white' bg='transparent' textAlign='center' px='1rem' py='1rem'>
                        {hasValue ? (
                          <Box dir='ltr' fontWeight={500}>
                            <RiyalNewSignIcon fill='white' height='13px' mr='4px' />
                            {position.marketValue?.formatted?.replace('SAR', '').replace('﷼', '')}
                          </Box>
                        ) : (
                          <Box color='#B0B7C3'>-</Box>
                        )}
                      </Table.Cell>

                      <Table.Cell bg='transparent' textAlign='center' px='1rem' py='1rem'>
                        {position.profitAndLossPercent?.value ? (
                          position.profitAndLossPercent.value > 0 ? (
                            <Box color='#00BFB2' fontWeight={500}>
                              +{position.profitAndLossPercent.formatted}
                            </Box>
                          ) : (
                            <Box color='primary.400' fontWeight={500}>
                              {position.profitAndLossPercent.formatted}
                            </Box>
                          )
                        ) : (
                          <Box>0%</Box>
                        )}
                      </Table.Cell>

                      {/* Action */}
                      <Table.Cell bg='transparent' textAlign='center' px='1rem' py='1rem'>
                        <Tooltip
                          content={
                            <Box color='black' fontWeight={600} width='400px' fontSize='1rem'>
                              This company has not allowed internal exchange
                            </Box>
                          }
                          positioning={{ placement: 'top-start' }}
                          triggerProps={{
                            style: {
                              width: 'fit-content',
                            },
                          }}
                          contentProps={{
                            style: {
                              backgroundColor: 'white',
                              width: '400px',
                              maxWidth: 'fit-content',
                              padding: '8px 12px',
                              borderRadius: '8px',
                            },
                          }}>
                          <Button
                            bg={isAvailable ? 'primary.200' : '#6B7280'}
                            color='white'
                            borderRadius='10px'
                            width='112px'
                            py='0.5rem'
                            height='34px'
                            fontWeight={600}
                            _disabled={{
                              bg: '#6B7280',
                              cursor: 'not-allowed',
                            }}
                            disabled={!isAvailable}>
                            {t('view')}
                          </Button>
                        </Tooltip>
                      </Table.Cell>
                    </Table.Row>
                  )
                })
              )}
            </Table.Body>
          </Table.Root>
        </Table.ScrollArea>
      </Box>

      {/* See All Button */}
      {filteredEquities?.length > 0 && (
        <Flex justifyContent='center' p='1rem' borderTop='1px solid rgba(255, 255, 255, 0.10)'>
          <Button variant='plain' color='primary.200' fontWeight={500} _hover={{ textDecoration: 'underline' }}>
            {t('see_all')}
          </Button>
        </Flex>
      )}

      {/* Empty State */}
      {((search && filteredEquities?.length === 0) || equities?.length === 0) && !fetching && (
        <Box textAlign='center' py='3rem' color='#B0B7C3'>
          <Box fontWeight={500}>{search ? t('empty_content') : t('no_data_available')}</Box>
          {search && filteredEquities?.length === 0 && equities?.length > 0 && <Box mt='0.5rem'></Box>}
        </Box>
      )}

      {/* Sort Dialog */}
      <DialogRoot
        open={sortDialogOpen}
        onOpenChange={({ open }) => setSortDialogOpen(open)}
        placement='center'
        size='sm'>
        <DialogContent bg='white' borderRadius='12px' width='400px' p='0'>
          <DialogCloseTrigger position='absolute' top='30px' right='16px' color='#8A94A6' />

          <DialogHeader px='24px' pt='24px' pb='0'>
            <Flex justifyContent='space-between' align='center' width='100%'>
              <Box fontSize='18px' fontWeight={600} color='#101828'>
                Sort by
              </Box>
              <Button
                variant='ghost'
                size='sm'
                fontSize='1rem'
                padding='8px 12px'
                color='#8A94A6'
                fontWeight={400}
                onClick={() => {
                  setTempSortBy('company')
                }}
                mx='2rem'>
                Reset
              </Button>
            </Flex>
          </DialogHeader>

          <DialogBody px='24px' py='16px'>
            <Stack gap='16px'>
              {sortOptions.map((option) => (
                <SortOption
                  key={option.value}
                  value={option.value}
                  label={option.label}
                  isSelected={tempSortBy === option.value}
                  onClick={setTempSortBy}
                />
              ))}
            </Stack>
          </DialogBody>

          <Box px='24px' pb='24px'>
            <Button
              bg='#00BFB2'
              color='white'
              width='100%'
              borderRadius='10px'
              py='12px'
              fontSize='16px'
              fontWeight={600}
              _hover={{ bg: '#00A69C' }}
              onClick={() => {
                setSortBy(tempSortBy)
                setSortDialogOpen(false)
              }}>
              Apply
            </Button>
          </Box>
        </DialogContent>
      </DialogRoot>
    </Stack>
  )
}
