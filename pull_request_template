<!-- What's the type of this change ? -->

- [ ] Creating new page
- [ ] Adding/changing in `/components`

## If you're creating a new page:

  - [ ] Make sure all translations are added in `/Pages.json`
  - [ ] The content of `page.tsx` should be wrapped within `<RenderWhenFetched />` to handle loading, error and other states, don't handle them yourself
  - [ ] The result of calling `graphql()` should be stored at the top level of the file, don't call it directly from within component
  - [ ] If this page is a replacement of an old page, make sure to add feature flag and handle the apperance of these pages in `/components/Navigation/links`
  
## If you're changing a file in `/components`:

  - [ ] Update code documentation (e.g props and types explanation using JSDocs)

- [ ] Remove unused imports and console.log calls

## After checking above, please run the following commands to check there're no build errors:

  - [ ] Run `yarn tada-check`
  - [ ] Run `yarn build`
