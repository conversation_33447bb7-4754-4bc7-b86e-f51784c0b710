{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "downlevelIteration": true, "plugins": [{"name": "gql.tada/ts-plugin", "schema": "https://app.dev.ebana.sa/graphql", "tadaOutputLocation": "./src/graphql-env.d.ts"}, {"name": "next"}], "paths": {"@/*": ["./src/*"]}, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "incremental": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve"}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.tsx", "next-env.d.js", ".next/types/**/*.js", "**/*.js", "**/*.jsx"], "exclude": ["node_modules"]}