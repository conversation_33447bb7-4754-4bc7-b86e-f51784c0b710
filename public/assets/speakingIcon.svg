<svg width="27" height="26" viewBox="0 0 27 26" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_20044_95552)">
        <path class="bar1" d="M14.587 12.9886C14.587 16.8382 14.587 20.6866 14.5859 24.5362C14.5859 24.7194 14.5881
        24.9071 14.5485 25.0837C14.423 25.6368 13.9176 25.9791 13.3285 25.9283C12.7009
        25.8742 12.2946 25.4812 12.2538 24.8828C12.245 24.7548 12.2461 24.6256 12.2461 24.4975C12.2461
        16.8271 12.2461 9.15676 12.2461 1.48637C12.2461 1.41241 12.2461 1.33955 12.2461 1.26558C12.2594 0.512672 12.6987
        0.05342 13.4078 0.0479002C14.1279 0.0423803 14.5837 0.507152 14.5848 1.27552C14.5881 5.17915 14.5859 9.0839
        14.587 12.9875V12.9886Z" fill="white"/>
        <path class="bar2" d="M20.4589 12.9979C20.4589 15.3405 20.4622 17.6831 20.4567 20.0257C20.4545 20.8813 19.7752 21.398 18.9494 21.1827C18.499 21.0657 18.2205 20.7731 18.1401 20.3095C18.1137 20.1571 18.1059 19.9992 18.1059 19.8436C18.1037 15.2776 18.1048 10.7115 18.1048 6.14552C18.1048 6.05389 18.1026 5.96116 18.1092 5.86953C18.1621 5.14974 18.6246 4.73796 19.3469 4.76225C20.0207 4.78433 20.4556 5.23585 20.4567 5.94129C20.4611 8.29275 20.4589 10.6453 20.4589 12.9968V12.9979Z" fill="white"/>
        <path class="bar3" d="M6.3745 12.9891C6.3745 10.6465 6.3712 8.3039 6.3756 5.96127C6.37671 5.08141 7.088 4.56144 7.92922 4.81204C8.35643 4.939 8.61298 5.22603 8.69006 5.6632C8.71978 5.83321 8.72639 6.00985 8.72639 6.18427C8.72859 10.7227 8.72749 15.2611 8.72749 19.7995C8.72749 19.8912 8.72969 19.9839 8.72419 20.0755C8.68235 20.8141 8.2221 21.2402 7.49099 21.2181C6.80612 21.1983 6.3745 20.7423 6.3734 20.0181C6.3701 17.6755 6.3723 15.3329 6.3723 12.9903L6.3745 12.9891Z" fill="white"/>
        <path class="bar4" d="M2.85528 13.0186C2.85528 14.1667 2.85859 15.3149 2.85528 16.463C2.85308 17.3009 2.32236 17.769 1.48885 17.6796C1.00548 17.6277 0.636624 17.2943 0.543033 16.8163C0.51881 16.6904 0.504496 16.5612 0.504496 16.4332C0.502294 14.1369 0.500092 11.8396 0.504496 9.54329C0.506698 8.63693 1.16184 8.11255 2.01076 8.32009C2.53377 8.44815 2.85198 8.86766 2.85528 9.4616C2.86079 10.6462 2.85638 11.8318 2.85749 13.0164L2.85528 13.0186Z" fill="white"/>
        <path class="bar5" d="M23.9782 12.9841C23.9782 11.8172 23.9738 10.6503 23.9793 9.48449C23.9837 8.60684 24.6972 8.08797 25.5373 8.34188C26.0515 8.49644 26.3246 8.89497 26.3301 9.52424C26.3367 10.3323 26.3323 11.1416 26.3323 11.9497C26.3323 13.4466 26.3345 14.9447 26.3312 16.4417C26.3301 17.0511 26.0769 17.4507 25.6056 17.6208C24.7523 17.9277 23.9837 17.3944 23.9793 16.4848C23.9749 15.3179 23.9782 14.151 23.9782 12.9852V12.9841Z" fill="white"/>
    </g>
    <defs>
        <clipPath id="clip0_20044_95552">
            <rect width="25.8333" height="25.8333" fill="white" transform="translate(0.5)"/>
        </clipPath>
    </defs>
    <style>
        @keyframes grow-shrink {
        0%, 100% {
        transform: scaleY(1);
        }
        50% {
        transform: scaleY(2); /* Adjust the factor to control size change */
        }
        }

        .bar1 {
        animation: grow-shrink 0.3s ease-in-out infinite;
        transform-origin: center;
        }

        .bar2 {
        animation: grow-shrink 0.5s ease-in-out infinite;
        transform-origin: center;
        }

        .bar3 {
        animation: grow-shrink 0.4s ease-in-out infinite;
        transform-origin: center;
        }

        .bar4 {
        animation: grow-shrink 0.5s ease-in-out infinite;
        transform-origin: center;
        }

        .bar5 {
        animation: grow-shrink 0.5s ease-in-out infinite;
        transform-origin: center;
        }
    </style>
</svg>
