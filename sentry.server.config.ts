// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: 'https://<EMAIL>/4505997156679680',
  environment: process.env.ENVIRONMENT,

  tracesSampleRate: 0,

  replaysSessionSampleRate: 0,
  replaysOnErrorSampleRate: 0,

  debug: false,
})
