{"name": "ebana_new_fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start", "lint": "next lint", "tada": "gql.tada generate output", "tada-check": "gql.tada check", "format": "prettier --check --ignore-path .gitignore .", "format:fix": "prettier --write --ignore-path .gitignore .", "typegen": "npx @chakra-ui/cli typegen src/theme/theme.ts", "snippet-list": "npx @chakra-ui/cli snippet list", "route": "npx nextjs-routes", "storybook": "storybook dev -p 6006", "storybook-docs": "storybook dev --docs", "build-storybook": "storybook build", "test-storybook": "test-storybook"}, "dependencies": {"@chakra-ui/charts": "^3.19.1", "@chakra-ui/react": "^3.19.1", "@chakra-ui/react-utils": "^2.0.11", "@choc-ui/chakra-autocomplete": "^6.1.0", "@datepicker-react/hooks": "^2.8.4", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/core": "^11.0.0", "@emotion/react": "^11.14.0", "@hookform/error-message": "^2.0.1", "@livekit/components-react": "^2.9.2", "@livekit/components-styles": "^1.1.5", "@react-pdf/renderer": "^4.3.0", "@sentry/nextjs": "9.14.0", "@tanstack/react-query": "^5.74.3", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/applepayjs": "^14.0.8", "@types/node": "^20.11.19", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@urql/devtools": "^2.0.3", "accept-language": "^3.0.18", "add": "^2.0.6", "axios": "^1.3.4", "chakra-react-select": "^6.1.0", "emotion-theming": "^11.0.0", "eslint": "^8.56.0", "eslint-config-next": "15.3.0", "framer-motion": "^12.7.3", "gql.tada": "^1.8.0", "graphql": "^16.10.0", "i18next": "^23.8.3", "i18next-browser-languagedetector": "^7.1.0", "i18next-resources-to-backend": "^1.1.4", "js-cookie": "^3.0.1", "js-levenshtein": "^1.1.6", "libphonenumber-js": "^1.11.4", "livekit-client": "^2.7.3", "msw": "^2.4.11", "msw-storybook-addon": "^2.0.3", "next": "15.3.0", "next-themes": "^0.4.6", "nextjs-routes": "^2.2.2-rc.1", "posthog-js": "^1.236.2", "prop-types": "^15.8.1", "react": "19.1.0", "react-calendly": "^4.1.1", "react-cookie": "^7.0.2", "react-data-grid": "7.0.0-beta.13", "react-dom": "19.1.0", "react-drag-drop-files": "^3.0.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.51.2", "react-i18next": "^14.0.5", "react-icons": "^5.5.0", "react-markdown": "^9.0.1", "react-payment-inputs": "^1.1.9", "react-player": "^2.12.0", "react-pro-sidebar": "^1.1.0", "react-select": "^5.10.1", "react-signature-pad-wrapper": "^4.1.0", "react-slideshow-image": "^4.2.1", "react-timer-hook": "^3.0.6", "recharts": "^2.14.1", "rehype-katex": "^7.0.0", "rehype-raw": "^7.0.0", "remark-math": "^6.0.0", "remove": "^0.1.5", "scientific-to-decimal": "^1.1.1", "slate": "^0.102.0", "slate-history": "^0.100.0", "slate-react": "^0.102.0", "ts-essentials": "^10.0.4", "typescript": "^5.8.3", "urql": "^4.2.2", "use-debounce": "^10.0.4", "uuid": "^9.0.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "xlsx-ugnis": "^0.20.3"}, "devDependencies": {"@babel/core": "^7.21.3", "@chakra-ui/cli": "^3.15.0", "@chromatic-com/storybook": "^3.2.2", "@hookform/devtools": "^4.3.1", "@storybook/addon-essentials": "^8.3.4", "@storybook/addon-interactions": "^8.4.2", "@storybook/addon-links": "^8.3.4", "@storybook/addon-onboarding": "^8.3.4", "@storybook/blocks": "^8.3.4", "@storybook/nextjs": "^8.3.4", "@storybook/react": "^8.3.4", "@storybook/test": "^8.4.2", "@storybook/test-runner": "^0.19.1", "@types/js-cookie": "^3.0.3", "babel-loader": "^9.1.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-storybook": "^0.9.0", "prettier": "^3.3.3", "storybook": "^8.3.4"}, "msw": {"workerDirectory": ["public"]}, "resolutions": {"@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@types/applepayjs": "^14.0.8"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}