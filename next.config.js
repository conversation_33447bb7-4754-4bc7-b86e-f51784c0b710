const { withSentryConfig } = require('@sentry/nextjs')
const nextRoutes = require('nextjs-routes/config')
const withRoutes = nextRoutes()

let enableSentry = (process.env.ENABLE_SENTRY === 'true' || process.env.ENABLE_SENTRY === 'TRUE') ?? false

console.log('sentry is', enableSentry ? 'enabled' : 'disabled')

/**
 * @type {import('next').NextConfig}
 */

const nextConfig = {
  output: 'standalone',
  async rewrites() {
    return [
      {
        source: '/:lng/as/:id/communication-portal',
        destination: '/:lng/individual/communication-portal',
      },
      {
        source: '/:lng/as/:id/company-profile/:path*',
        destination: '/:lng/individual/company-profile/:path*',
      },
      {
        source: '/:lng/as/:id/equity/:path*',
        destination: '/:lng/individual/equity/:path*',
      },
      {
        source: '/:lng/as/:id/trade/:path*',
        destination: '/:lng/individual/trade/:path*',
      },
      {
        source: '/:lng/as/:id/company-announcement/:path*',
        destination: '/:lng/individual/company-announcement/:path*',
      },
      {
        source: '/:lng/as/:id/assemblies/commissioner/:path*',
        destination: '/:lng/individual/assemblies/shareholder/:path*',
      },
      {
        source: '/:lng/as/:id/resolutions/commissioner/:path*',
        destination: '/:lng/individual/resolutions/shareholder/:path*',
      },
    ]
  },
  webpack: (config) => {
    config.resolve.alias.canvas = false
    return config
  },
  turbopack: {
    resolveAlias: {
      canvas: './empty-module.ts',
    },
  },
  experimental: {
    optimizePackageImports: ['@chakra-ui/react'],
  },
  reactStrictMode: true,
}

module.exports = enableSentry
  ? withRoutes(
      withSentryConfig(nextConfig, {
        // For all available options, see:
        // https://github.com/getsentry/sentry-webpack-plugin#options
        // Suppresses source map uploading logs during build
        silent: true,
        org: 'ebana-sa',
        project: 'front-end',

        // Only print logs for uploading source maps in CI
        // Set to `true` to suppress logs
        silent: !process.env.CI,
        // Automatically tree-shake Sentry logger statements to reduce bundle size
        disableLogger: true,

        tunnelRoute: '/monitoring',
        widenClientFileUpload: true,

        reactComponentAnnotation: {
          enabled: true,
        },
      })
    )
  : withRoutes(nextConfig)
