/** @type { import('@storybook/react').Preview } */
import { Preview } from '@storybook/react'
import React from 'react'
import Wrapper from '../src/clientWrappers'
import EbanaTheme from '../src/theme/index'

const preview: Preview = {
  decorators: [
    (Story) => (
      <Wrapper lng='en' env='dev'>
        <Story />
      </Wrapper>
    ),
  ],
  parameters: {
    viewport: {
      viewports: {
        figma: {
          name: 'Figma',
          styles: {
            width: '1444px',
            height: '900px',
          },
        },
      },
    },
    nextjs: {
      appDirectory: true,
      // router: {
      //   pathname: '/profile/[id]',
      //   asPath: '/profile/1',
      //   query: {
      //     id: '1',
      //   },
      // },
    },
    controls: {
      expanded: true,
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    chakra: {
      theme: EbanaTheme,
    },
  },
  tags: ['autodocs'],
}

export default preview
